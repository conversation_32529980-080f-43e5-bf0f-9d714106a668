//
//  ContentView.swift
//  path_generator
//
//  Created by 杨冰冰 on 2025/5/26.
//

import SwiftUI
import UIKit

// MARK: - 绘制画布视图
struct DrawingCanvasView: UIViewRepresentable {
    @Binding var paths: [CGPath]
    @Binding var currentPath: CGPath?

    func makeUIView(context: Context) -> DrawingView {
        let view = DrawingView()
        view.delegate = context.coordinator
        return view
    }

    func updateUIView(_ uiView: DrawingView, context: Context) {
        uiView.paths = paths
        uiView.currentPath = currentPath
        uiView.setNeedsDisplay()
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, DrawingViewDelegate {
        var parent: DrawingCanvasView

        init(_ parent: DrawingCanvasView) {
            self.parent = parent
        }

        func pathDidUpdate(_ path: CGPath?) {
            parent.currentPath = path
        }

        func pathDidComplete(_ path: CGPath) {
            parent.paths.append(path)
            parent.currentPath = nil
        }
    }
}

// MARK: - 绘制视图协议
protocol DrawingViewDelegate: AnyObject {
    func pathDidUpdate(_ path: CGPath?)
    func pathDidComplete(_ path: CGPath)
}

// MARK: - 自定义绘制视图
class DrawingView: UIView {
    weak var delegate: DrawingViewDelegate?
    var paths: [CGPath] = []
    var currentPath: CGPath?

    private var pathGenerator: FittingPathGenerator!
    private var isDrawing = false

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupPathGenerator()
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupPathGenerator()
        setupView()
    }

    private func setupView() {
        backgroundColor = UIColor.systemBackground
        isMultipleTouchEnabled = false
    }

    private func setupPathGenerator() {
        var params = Params()
        params.calligraphyMinLineWidth = 2.0
        params.calligraphyMaxForce = 1.0
        params.calligraphyMinForce = 0.1
        params.radius = 8.0
        params.calligraphyMotionSmooth = 0.3
        params.calligraphyWidthSmooth = 0.5

        let taperConfig = TaperConfig(isEnable: false, startLength: 0, endLength: 0)
        pathGenerator = FittingPathGenerator(params: params, taperConfig: taperConfig)
        pathGenerator.updateRelativeView(self)
    }

    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }

        // 设置绘制样式
        context.setFillColor(UIColor.systemBlue.cgColor)
        context.setStrokeColor(UIColor.systemBlue.cgColor)
        context.setLineWidth(1.0)

        // 绘制已完成的路径
        for path in paths {
            context.addPath(path)
            context.fillPath()
        }

        // 绘制当前路径
        if let currentPath = currentPath {
            context.addPath(currentPath)
            context.fillPath()
        }
    }

    // MARK: - 触摸事件处理
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }

        let location = touch.location(in: self)
        let point = ZDPointUnit(location: location, force: touch.force, radius: 5.0)

        isDrawing = true
        _ = pathGenerator.began(point: point, touches: touches, event: event)

        updateCurrentPath()
    }

    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isDrawing, let touch = touches.first else { return }

        let location = touch.location(in: self)
        let point = ZDPointUnit(location: location, force: touch.force, radius: 5.0)

        _ = pathGenerator.push(point: point, touches: touches, event: event)

        updateCurrentPath()
    }

    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isDrawing, let touch = touches.first else { return }

        let location = touch.location(in: self)
        let point = ZDPointUnit(location: location, force: touch.force, radius: 5.0)

        _ = pathGenerator.end(point: point, touches: touches, event: event)

        if let finalPath = pathGenerator.pathRef() {
            delegate?.pathDidComplete(finalPath)
        }

        isDrawing = false
        pathGenerator.clean()
    }

    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        isDrawing = false
        pathGenerator.clean()
        delegate?.pathDidUpdate(nil)
    }

    private func updateCurrentPath() {
        let path = pathGenerator.pathRef()
        delegate?.pathDidUpdate(path)
        setNeedsDisplay()
    }

    func clearCanvas() {
        paths.removeAll()
        currentPath = nil
        pathGenerator.clean()
        setNeedsDisplay()
    }
}

// MARK: - 主视图
struct ContentView: View {
    @State private var paths: [CGPath] = []
    @State private var currentPath: CGPath?
    @State private var showTestResults = false
    @State private var testOutput = ""

    var body: some View {
        VStack(spacing: 20) {
            // 标题
            Text("FittingPathGenerator 演示")
                .font(.title)
                .fontWeight(.bold)
                .padding(.top)

            // 绘制画布
            DrawingCanvasView(paths: $paths, currentPath: $currentPath)
                .frame(height: 400)
                .border(Color.gray, width: 1)
                .padding(.horizontal)

            // 控制按钮
            HStack(spacing: 20) {
                Button("清除画布") {
                    paths.removeAll()
                    currentPath = nil
                }
                .buttonStyle(.bordered)

                Button("运行测试") {
                    runTests()
                }
                .buttonStyle(.borderedProminent)

                Button("生成示例路径") {
                    generateSamplePath()
                }
                .buttonStyle(.bordered)
            }
            .padding()

            // 测试结果显示
            if showTestResults {
                ScrollView {
                    Text(testOutput)
                        .font(.system(.caption, design: .monospaced))
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
                .frame(maxHeight: 150)
                .padding(.horizontal)
            }

            Spacer()
        }
    }

    private func runTests() {
        testOutput = ""

        // 重定向print输出到字符串
        var output = ""

        // 测试MathUtils
        output += "=== MathUtils 测试 ===\n"
        let pointA = CGPoint(x: 0, y: 0)
        let pointB = CGPoint(x: 10, y: 10)
        let interval: TimeInterval = 0.1

        let speed = MathUtils.getSpeedBetweenTwoPointsWithTimeInterval(pointA: pointA, pointB: pointB, interval: interval)
        output += "计算速度: \(speed)\n"

        let force = MathUtils.newBrushTransformSpeedToForce(speed, 1.0, 0.1, 1.0, fasterIsThick: true)
        output += "转换压力: \(force)\n\n"

        // 测试ZDPathUtils
        output += "=== ZDPathUtils 测试 ===\n"
        let center = CGPoint(x: 50, y: 50)
        let radius: CGFloat = 10

        let circlePath = ZDPathUtils.generateCircle(center: center, radius: radius)
        output += "生成圆形路径，边界框: \(circlePath.boundingBox)\n"

        let points = [CGPoint(x: 0, y: 0), CGPoint(x: 10, y: 10), CGPoint(x: 20, y: 0)]
        let closePath = ZDPathUtils.generateNormalClosePath(points: points)
        output += "生成闭合路径，边界框: \(closePath.boundingBox)\n\n"

        // 测试FittingPathGenerator
        output += "=== FittingPathGenerator 测试 ===\n"
        var params = Params()
        params.calligraphyMinLineWidth = 2.0
        params.calligraphyMaxForce = 1.0
        params.calligraphyMinForce = 0.1
        params.radius = 5.0

        let taperConfig = TaperConfig(isEnable: false, startLength: 0, endLength: 0)
        let pathGenerator = FittingPathGenerator(params: params, taperConfig: taperConfig)

        let point1 = ZDPointUnit(location: CGPoint(x: 10, y: 10), force: 0.5, radius: 3.0)
        let point2 = ZDPointUnit(location: CGPoint(x: 20, y: 20), force: 0.7, radius: 4.0)
        let point3 = ZDPointUnit(location: CGPoint(x: 30, y: 15), force: 0.6, radius: 3.5)

        let effectArea1 = pathGenerator.began(point: point1, touches: nil, event: nil)
        output += "开始点效果区域: \(effectArea1)\n"

        let effectArea2 = pathGenerator.push(point: point2, touches: nil, event: nil)
        output += "推进点效果区域: \(effectArea2)\n"

        let effectArea3 = pathGenerator.end(point: point3, touches: nil, event: nil)
        output += "结束点效果区域: \(effectArea3)\n"

        if let path = pathGenerator.pathRef() {
            output += "成功生成路径，边界框: \(path.boundingBox)\n"
        } else {
            output += "路径生成失败\n"
        }

        let availablePoints = pathGenerator.avaliablePoints()
        output += "可用点数量: \(availablePoints.count)\n"

        output += "=== 测试完成 ===\n"

        testOutput = output
        showTestResults = true
    }

    private func generateSamplePath() {
        // 生成一个示例路径来展示效果
        var params = Params()
        params.calligraphyMinLineWidth = 2.0
        params.calligraphyMaxForce = 1.0
        params.calligraphyMinForce = 0.1
        params.radius = 8.0

        let taperConfig = TaperConfig(isEnable: false, startLength: 0, endLength: 0)
        let pathGenerator = FittingPathGenerator(params: params, taperConfig: taperConfig)

        // 创建一个波浪形的示例路径
        let centerX: CGFloat = 200
        let centerY: CGFloat = 200
        let amplitude: CGFloat = 50
        let frequency: CGFloat = 0.1

        var samplePoints: [ZDPointUnit] = []
        for i in 0...50 {
            let x = centerX + CGFloat(i) * 4
            let y = centerY + sin(CGFloat(i) * frequency) * amplitude
            let force = 0.3 + 0.4 * sin(CGFloat(i) * 0.2) // 变化的压力
            let point = ZDPointUnit(location: CGPoint(x: x, y: y), force: force, radius: 5.0)
            samplePoints.append(point)
        }

        // 生成路径
        if let firstPoint = samplePoints.first {
            _ = pathGenerator.began(point: firstPoint, touches: nil, event: nil)
        }

        for i in 1..<(samplePoints.count - 1) {
            _ = pathGenerator.push(point: samplePoints[i], touches: nil, event: nil)
        }

        if let lastPoint = samplePoints.last {
            _ = pathGenerator.end(point: lastPoint, touches: nil, event: nil)
        }

        if let generatedPath = pathGenerator.pathRef() {
            paths.append(generatedPath)
        }
    }
}

#Preview {
    ContentView()
}
