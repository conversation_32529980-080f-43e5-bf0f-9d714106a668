//
//  FittingPathGenerator.swift
//  PathGenerator
//
//  Created by AI Assistant
//  重构自FittingPathGenerator_v2，完全自包含，不依赖其他文件
//

import Foundation
import UIKit
import simd

// MARK: - 基础类型定义

/// 影响区域类型别名
public typealias EffectArea = CGRect

/// 路径生成器协议
public protocol PathGeneratorType {
    func began(point: ZDPointUnit, touches: Set<UITouch>?, event: UIEvent?) -> EffectArea
    func push(point: ZDPointUnit, touches: Set<UITouch>?, event: UIEvent?) -> EffectArea
    func end(point: ZDPointUnit, touches: Set<UITouch>?, event: UIEvent?) -> EffectArea
    func updateRelativeView(_ view: UIView)
    func pathRef() -> CGPath?
    func avaliablePoints() -> [ZDPointUnit]
}

// MARK: - vector_double2 扩展

extension vector_double2 {
    /// 初始化向量，召唤一个从A指向B的向量 A-->B
    init(_ A: CGPoint, _ B: CGPoint) {
        self.init(x: B.x-A.x, y: B.y-A.y)
    }

    /// 此向量的长度
    var length: Double {
        return simd_length(self)
    }

    /// 此向量的长度(损失一定精度)
    var fastLength: Double {
        return simd_fast_length(self)
    }

    ///返回向量长度的平方
    var lengthSqrt: Double {
        return simd_length_squared(self)
    }

    ///与该向量的垂直向量（在该向量的逆时针角度）
    var verticalVector: vector_double2 {
        return vector_double2(-self.y, self.x)
    }

    /// 此向量的法向量
    var normalLize: vector_double2 {
        return simd_normalize(self)
    }

    /// x轴
    static var xAxis: vector_double2 {
        return vector_double2(x: 1, y: 0)
    }

    /// y轴
    static var yAxis: vector_double2 {
        return vector_double2(x: 0, y: 1)
    }

    /// 此向量的法向量(损失精度)
    var fastNormalLize: vector_double2 {
        return simd_fast_normalize(self)
    }

    static func*(_ AB: vector_double2, _ AC: vector_double2) -> Double {
        return simd_dot(AB, AC)
    }

    static func+(_ AB: vector_double2, _ BC: vector_double2) -> vector_double2 {
        return vector_double2(x: AB.x+BC.x, y: AB.y+BC.y)
    }

    static func*(_ n: Double, _ AB: vector_double2) -> vector_double2 {
        return vector_double2(x: AB.x*n, y: AB.y*n)
    }

    /// 此向量在另一向量方向上的投影
    func fastProject(on B: vector_double2) -> vector_double2 {
        simd_fast_project(self, B)
    }

    /// 两向量的夹角
    static func angle(from AB: vector_double2, to AC: vector_double2) -> Double {
        let dot = simd_dot(AB, AC)
        let length = AB.length*AC.length
        var cos = dot/(length == 0 ? 1 : length)
        let orient = simd_orient(AB, AC)

        if cos > 1 {
            cos = 1
        } else if cos < -1 {
            cos = -1
        }
        var angle = acos(cos)

        if orient > 0 {
            angle = -angle
        }
        return angle
    }

    static func cos(from AB: vector_double2, to AC: vector_double2) -> Double {
        let dot = simd_dot(AB, AC)
        let length = AB.length*AC.length
        var cos = dot/(length == 0 ? 1 : length)

        if cos > 1 {
            cos = 1
        } else if cos < -1 {
            cos = -1
        }
        return cos
    }

    /// 向量的斜率
    var k: Double? {
        if x == 0 {
            return nil // 垂直线
        }
        return y / x
    }

    /// 将向量转换为CGPoint
    var point: CGPoint {
        return CGPoint(x: x, y: y)
    }

    /// 计算两个向量的角平分线
    static func bisector(from v1: vector_double2, to v2: vector_double2) -> vector_double2 {
        let n1 = v1.fastNormalLize
        let n2 = v2.fastNormalLize
        return (n1 + n2).fastNormalLize
    }
}

// MARK: - CGPoint 扩展

extension CGPoint {
    static func - (lhs: CGPoint, rhs: CGPoint) -> vector_double2 {
        return vector_double2(x: lhs.x - rhs.x, y: lhs.y - rhs.y)
    }

    static func + (lhs: CGPoint, rhs: vector_double2) -> CGPoint {
        return CGPoint(x: lhs.x + rhs.x, y: lhs.y + rhs.y)
    }

    var vector: vector_double2 {
        return vector_double2(x: x, y: y)
    }

    func mid(to point: CGPoint) -> CGPoint {
        return CGPoint(x: (x + point.x) * 0.5, y: (y + point.y) * 0.5)
    }

    func distanceTo(point: CGPoint) -> CGFloat {
        return sqrt(pow(x - point.x, 2) + pow(y - point.y, 2))
    }

    static func middle(p1: CGPoint, p2: CGPoint) -> CGPoint {
        return CGPoint(x: (p1.x + p2.x) * 0.5, y: (p1.y + p2.y) * 0.5)
    }

    func makeRect(width: Double, height: Double) -> CGRect {
        return CGRect(x: x - width/2, y: y - height/2, width: width, height: height)
    }

    func multiplyBy(value: CGFloat) -> CGPoint {
        return CGPoint(x: x * value, y: y * value)
    }

    var length: CGFloat {
        return sqrt(x * x + y * y)
    }
}

// MARK: - CGRect 扩展

extension CGRect {
    static func + (lhs: CGRect, rhs: CGRect) -> CGRect {
        return lhs.union(rhs)
    }
}

// MARK: - ZDPointUnit 相关类型

/// 绘制点类型
public enum ZDPointType: Int, Codable {
    case origin = 0
    case auxiliary = 1
}

/// 触摸类型
public enum ZDTouchType: Int, Codable {
    case none = 0
    case finger = 1
    case pencil = 2
}

/// 点协议
public protocol Point2DRepresentable {
    var xValue: Float { get }
    var yValue: Float { get }
    var cgPoint: CGPoint { get }
}

/// 绘制点
public struct ZDPointUnit {
    public var location = CGPoint.zero
    public var force: CGFloat = 1.0
    public var pointType: ZDPointType = .origin
    public var touchType: ZDTouchType = .none
    public var timeStamp: TimeInterval?
    public var altitude: CGFloat?
    public var azimuth: CGFloat?
    public var radius: CGFloat?

    // Pencil only.
    public var estimatedProperties: UITouch.Properties = []
    public var estimatedPropertiesExpectingUpdates: UITouch.Properties = []

    var perpendicularForce: CGFloat {
        let force = force
        if let altitude = altitude {
            let result = force * CGFloat(sin(Double(altitude)))
            return result
        } else {
            return force
        }
    }

    public init(
        location: CGPoint,
        force: CGFloat = 1.0,
        pointType: ZDPointType = ZDPointType.origin,
        toucheType: ZDTouchType = ZDTouchType.none,
        timeStamp: TimeInterval? = Optional.none,
        altitude: CGFloat? = Optional.none,
        azimuth: CGFloat? = Optional.none,
        radius: CGFloat? = Optional.none
    ) {
        self.location = location
        self.force = force
        self.pointType = pointType
        self.touchType = toucheType
        self.timeStamp = timeStamp
        self.altitude = altitude
        self.azimuth = azimuth
        self.radius = radius
    }

    public init(touch: UITouch, inView: UIView) {
        self.init(location: touch.location(in: inView),
                  force: touch.force,
                  pointType: .origin,
                  toucheType: touch.type == .pencil ? .pencil : .finger,
                  timeStamp: touch.timestamp)
    }

    public init(point: CGPoint, touch: UITouch, azimuth: CGFloat? = Optional.none) {
        self.init(location: point,
                  force: touch.force,
                  pointType: .origin,
                  toucheType: touch.type == .pencil ? .pencil : .finger,
                  timeStamp: touch.timestamp,
                  altitude: touch.altitudeAngle,
                  azimuth: azimuth)
    }

    private func copy() -> ZDPointUnit {
        return ZDPointUnit(location: location, force: force, pointType: pointType, toucheType: touchType, timeStamp: timeStamp, altitude: altitude, azimuth: azimuth, radius: radius)
    }

    public func offset(_ locationOffset: CGPoint) -> ZDPointUnit {
        let newLocation = self.location.applying(.init(translationX: locationOffset.x, y: locationOffset.y))
        var newPoint = self.copy()
        newPoint.location = newLocation
        return newPoint
    }

    public func withRadius(_ radius: CGFloat) -> ZDPointUnit {
        var newPoint = self.copy()
        newPoint.radius = radius
        return newPoint
    }

    public func withLocation(_ location: CGPoint) -> ZDPointUnit {
        var newPoint = self.copy()
        newPoint.location = location
        return newPoint
    }

    public static func middle(p1: ZDPointUnit, p2: ZDPointUnit, pointType: ZDPointType = .auxiliary) -> ZDPointUnit {
        let location = CGPoint(x: (p1.location.x + p2.location.x) * 0.5, y: (p1.location.y + p2.location.y) * 0.5)
        var radius: Optional<CGFloat> = nil
        if let r1 = p1.radius, let r2 = p2.radius {
            radius = (r1 + r2) * 0.5
        }
        var altitude: Optional<CGFloat> = nil
        if let altitude1 = p1.altitude, let altitude2 = p2.altitude {
            altitude = (altitude1 + altitude2) * 0.5
        }
        var azimuth: Optional<CGFloat> = nil
        if let azimuth1 = p1.azimuth, let azimuth2 = p2.azimuth {
            azimuth = (azimuth1 + azimuth2) * 0.5
        }
        return ZDPointUnit(location: location,
                           force: (p1.force + p2.force) / 2.0,
                           pointType: pointType,
                           toucheType: p1.touchType,
                           altitude: altitude,
                           azimuth: azimuth,
                           radius: radius
        )
    }

    public static func distance(p1: ZDPointUnit, p2: ZDPointUnit) -> CGFloat {
        return p1.location.distanceTo(point: p2.location)
    }

    public static var zero: ZDPointUnit {
        return self.init(location: .zero)
    }
}

extension ZDPointUnit: Equatable {
    public static func == (lhs: Self, rhs: Self) -> Bool {
        var equal = lhs.location == rhs.location
        equal = equal || lhs.force == rhs.force
        return equal
    }
}

extension ZDPointUnit: Point2DRepresentable {
    public var xValue: Float {
        return Float(self.location.x)
    }

    public var yValue: Float {
        return Float(self.location.y)
    }

    public var cgPoint: CGPoint {
        return self.location
    }
}

// MARK: - KiloLineEnd 相关类型

/// 线点协议
public protocol LinePoint {
    var point: CGPoint { get set }
}

extension LinePoint {
    static func - (lhs: Self, rhs: Self) -> vector_double2 {
        lhs.point - rhs.point
    }

    static func + (lhs: inout Self, rhs: vector_double2) {
        lhs.point = lhs.point + rhs
    }

    static func + (lhs: Self, rhs: vector_double2) -> Self {
        var a = lhs
        a.point = a.point + rhs
        return a
    }
}

extension CGPoint: LinePoint {
    public var point: CGPoint {
        get { return self }
        set { self = newValue }
    }
}

/// 点过滤器
public struct KiloLineEnd<T: LinePoint> {
    private var lim_a: CGFloat
    private var lim_b: CGFloat
    private var lim_c: CGFloat

    public init(lim_a: CGFloat, lim_b: CGFloat, lim_c: CGFloat) {
        self.lim_a = lim_a
        self.lim_b = lim_b
        self.lim_c = lim_c
    }

    public mutating func updateParameter(lim_a: CGFloat, lim_c: CGFloat) {
        self.lim_a = lim_a
        self.lim_c = lim_c
    }

    public func filter(points: [T]) -> [T] {
        guard points.count > 2 else { return points }

        var filteredPoints: [T] = []
        filteredPoints.append(points[0])

        for i in 1..<(points.count - 1) {
            let prev = points[i - 1]
            let current = points[i]
            let next = points[i + 1]

            let d1 = prev.point.distanceTo(point: current.point)
            let d2 = current.point.distanceTo(point: next.point)

            if d1 > lim_a || d2 > lim_a {
                filteredPoints.append(current)
            }
        }

        if points.count > 1 {
            filteredPoints.append(points.last!)
        }

        return filteredPoints
    }
}

// MARK: - MathUtils 工具类

public struct MathUtils {

    /// 计算两点间速度
    public static func getSpeedBetweenTwoPointsWithTimeInterval(pointA: CGPoint, pointB: CGPoint, interval: TimeInterval) -> CGFloat {
        let distance = pointA.distanceTo(point: pointB)
        if interval <= 0 {
            return 0
        }
        return distance / CGFloat(interval)
    }

    /// 将速度转换为压力
    public static func newBrushTransformSpeedToForce(_ speed: CGFloat, _ maxForce: CGFloat, _ minForce: CGFloat, _ scale: CGFloat, fasterIsThick: Bool = false) -> CGFloat {
        let normalizedSpeed = speed / scale
        let speedFactor = min(normalizedSpeed / 1000.0, 1.0)

        if fasterIsThick {
            return minForce + (maxForce - minForce) * speedFactor
        } else {
            return maxForce - (maxForce - minForce) * speedFactor
        }
    }

    /// 获取两个圆的四个切点
    public static func getFourContactPointsOfTwoCircle(center1: CGPoint, radius1: CGFloat, center2: CGPoint, radius2: CGFloat) -> (CGPoint, CGPoint, CGPoint, CGPoint)? {
        let distance = center1.distanceTo(point: center2)

        // 如果圆心距离小于半径差，说明一个圆在另一个圆内部
        if distance < abs(radius1 - radius2) {
            return nil
        }

        // 如果圆心距离等于0，说明是同心圆
        if distance == 0 {
            return nil
        }

        let dx = center2.x - center1.x
        let dy = center2.y - center1.y

        // 外切线
        let a = (radius1 - radius2) / distance
        let h = sqrt(1 - a * a)

        let px = center1.x + a * dx
        let py = center1.y + a * dy

        let t1x = px + h * dy
        let t1y = py - h * dx
        let t2x = px - h * dy
        let t2y = py + h * dx

        // 内切线
        let b = (radius1 + radius2) / distance
        if b > 1 {
            // 两圆相交，没有内切线
            let t1 = CGPoint(x: t1x, y: t1y)
            let t2 = CGPoint(x: t2x, y: t2y)
            return (t1, t2, t1, t2)
        }

        let h2 = sqrt(1 - b * b)
        let qx = center1.x + b * dx
        let qy = center1.y + b * dy

        let t3x = qx + h2 * dy
        let t3y = qy - h2 * dx
        let t4x = qx - h2 * dy
        let t4y = qy + h2 * dx

        return (CGPoint(x: t1x, y: t1y), CGPoint(x: t2x, y: t2y), CGPoint(x: t3x, y: t3y), CGPoint(x: t4x, y: t4y))
    }

    /// 添加弧线到路径
    public static func addArcToPathWithTwoPointsOnCircle(path: CGMutablePath, center: CGPoint, radius: CGFloat, startPoint: CGPoint, endPoint: CGPoint, clockwise: Bool) {
        let startAngle = atan2(startPoint.y - center.y, startPoint.x - center.x)
        let endAngle = atan2(endPoint.y - center.y, endPoint.x - center.x)

        path.addArc(center: center, radius: radius, startAngle: startAngle, endAngle: endAngle, clockwise: clockwise)
    }

    /// 计算两个向量的夹角
    public static func getAngleOfTwoVectorsWithPoints(center: CGPoint, point1: CGPoint, point2: CGPoint) -> CGFloat {
        let v1 = vector_double2(center, point1)
        let v2 = vector_double2(center, point2)
        return CGFloat(vector_double2.angle(from: v1, to: v2))
    }

    /// 判断顺时针方向
    public static func getClockWise(center: CGPoint, point1: CGPoint, point2: CGPoint) -> Bool {
        let v1 = vector_double2(center, point1)
        let v2 = vector_double2(center, point2)
        return simd_orient(v1, v2) > 0
    }

    /// 获取圆上的点
    public static func getPointOnCircleWithOtherPoint(center: CGPoint, radius: CGFloat, otherPoint: CGPoint, clockwise: Bool) -> CGPoint {
        let angle = atan2(otherPoint.y - center.y, otherPoint.x - center.x)
        let adjustedAngle = clockwise ? angle + CGFloat.pi / 2 : angle - CGFloat.pi / 2

        let x = center.x + radius * cos(adjustedAngle)
        let y = center.y + radius * sin(adjustedAngle)

        return CGPoint(x: x, y: y)
    }
}

// MARK: - ZDPathUtils 工具类

public struct ZDPathUtils {

    /// 生成普通闭合路径
    public static func generateNormalClosePath(points: [CGPoint]) -> CGPath {
        let path = CGMutablePath()
        guard points.count > 0 else { return path }

        path.move(to: points[0])
        for i in 1..<points.count {
            path.addLine(to: points[i])
        }
        path.closeSubpath()

        return path
    }

    /// 生成圆形路径
    public static func generateCircle(center: CGPoint, radius: CGFloat) -> CGPath {
        let path = CGMutablePath()
        path.addEllipse(in: CGRect(x: center.x - radius, y: center.y - radius, width: radius * 2, height: radius * 2))
        return path
    }

    /// 生成贝塞尔曲线路径
    public static func generateBezierPath(startPoint: CGPoint, controlPoint1: CGPoint, controlPoint2: CGPoint, endPoint: CGPoint) -> CGPath {
        let path = CGMutablePath()
        path.move(to: startPoint)
        path.addCurve(to: endPoint, control1: controlPoint1, control2: controlPoint2)
        return path
    }

    /// 生成平滑曲线路径
    public static func generateSmoothPath(points: [CGPoint]) -> CGPath {
        let path = CGMutablePath()
        guard points.count > 1 else { return path }

        path.move(to: points[0])

        if points.count == 2 {
            path.addLine(to: points[1])
            return path
        }

        for i in 1..<points.count {
            let currentPoint = points[i]
            let previousPoint = points[i-1]
            let controlPoint = CGPoint(x: (currentPoint.x + previousPoint.x) / 2, y: (currentPoint.y + previousPoint.y) / 2)

            if i == 1 {
                path.addQuadCurve(to: controlPoint, control: previousPoint)
            } else {
                path.addQuadCurve(to: controlPoint, control: previousPoint)
            }
        }

        path.addLine(to: points.last!)
        return path
    }
}

// MARK: - FittingPathGenerator 辅助类型

/// 点单元引用
public class PointUnitRef {
    public var point: ZDPointUnit

    public init(point: ZDPointUnit) {
        self.point = point
    }

    public func update(point: ZDPointUnit) {
        self.point = point
    }
}

/// 渐变配置
public struct TaperConfig {
    public var isEnable: Bool = false
    public var startLength: CGFloat = 0
    public var endLength: CGFloat = 0

    public init(isEnable: Bool = false, startLength: CGFloat = 0, endLength: CGFloat = 0) {
        self.isEnable = isEnable
        self.startLength = startLength
        self.endLength = endLength
    }
}

/// 渐变状态
public struct TaperState {
    public var currentLength: CGFloat = 0
    public var totalLength: CGFloat = 0

    public init() {}

    public mutating func reset() {
        currentLength = 0
        totalLength = 0
    }
}

/// 参数配置
public struct Params {
    public var calligraphyMinLineWidth: CGFloat = 1.0
    public var calligraphyMaxForce: CGFloat = 1.0
    public var calligraphyMinForce: CGFloat = 0.1
    public var calligraphyMotionSmooth: CGFloat = 0.5
    public var calligraphyWidthSmooth: CGFloat = 0.5
    public var radius: CGFloat = 5.0

    public init() {}
}

/// 缓存关键点
public struct CacheKeyPoint {
    public var lastUpKeyPointA: CGPoint?
    public var lastDownKeyPointA: CGPoint?

    public init(lastUpKeyPointA: CGPoint?, lastDownKeyPointA: CGPoint?) {
        self.lastUpKeyPointA = lastUpKeyPointA
        self.lastDownKeyPointA = lastDownKeyPointA
    }
}

/// 渲染上下文
public struct RenderContext {
    public var keyPoint: CacheKeyPoint
    public var isStart: Bool
    public var isEnd: Bool

    public init(keyPoint: CacheKeyPoint, isStart: Bool, isEnd: Bool) {
        self.keyPoint = keyPoint
        self.isStart = isStart
        self.isEnd = isEnd
    }

    public func withKeyPoint(newKeyPoint: CacheKeyPoint) -> RenderContext {
        return RenderContext(keyPoint: newKeyPoint, isStart: isStart, isEnd: isEnd)
    }

    public func withIsStart(isStart: Bool) -> RenderContext {
        return RenderContext(keyPoint: keyPoint, isStart: isStart, isEnd: isEnd)
    }

    public func withIsEnd(isEnd: Bool) -> RenderContext {
        return RenderContext(keyPoint: keyPoint, isStart: isStart, isEnd: isEnd)
    }
}

/// 切线单元
public struct TangencyUnit {
    public var tangencyBStartPointAndAngle: (CGPoint, CGFloat)
    public var tangencyBEndPointAndAngle: (CGPoint, CGFloat)

    public init(tangencyBStartPointAndAngle: (CGPoint, CGFloat), tangencyBEndPointAndAngle: (CGPoint, CGFloat)) {
        self.tangencyBStartPointAndAngle = tangencyBStartPointAndAngle
        self.tangencyBEndPointAndAngle = tangencyBEndPointAndAngle
    }
}

// MARK: - FittingPathGenerator 核心类

/// 力度拟合路径生成器
public class FittingPathGenerator: PathGeneratorType {

    // MARK: - 私有属性

    /// 确认的路径
    private var confirmPath: CGMutablePath = CGMutablePath()

    /// 缓存路径
    private var cachePath: CGMutablePath = CGMutablePath()

    /// 采样路径
    private var samplingPath: CGMutablePath = CGMutablePath()

    /// 确认的点
    private var confirmedPoints: [PointUnitRef] = []

    /// 缓存的点
    private var cachingPoints: [PointUnitRef] = []

    /// 渲染上下文
    private var renderContext: RenderContext?

    /// 是否使用压力模式
    private var isForceMode: Bool = true

    /// 渐变配置
    private var taperConfig: TaperConfig = TaperConfig()

    /// 视图引用
    private weak var view: UIView?

    /// 是否在第一个点约束的很小范围内
    @available(*, deprecated, message: "暂时没有用到")
    private var isInFirstPointDomain: Bool = true

    /// 记录画布缩放比例
    private var scale: CGFloat = 1.0

    /// 通过算法来过滤一些不必要的点
    private var pathPolishTool: KiloLineEnd = KiloLineEnd<CGPoint>(lim_a: 3, lim_b: 8, lim_c: 4)

    /// 配置参数，只读
    private let params: Params

    /// 渐变状态
    private var taperState: TaperState = .init()

    /// 最小半径
    var minRadius: CGFloat { (params.calligraphyMinLineWidth + 0.01) * params.radius }

    /// 最大半径 - 修复递归调用问题
    var maxRadius: CGFloat {
        // 直接计算最大半径，避免调用generateRadiusWithForce造成递归
        let maxForce = params.calligraphyMaxForce
        let normalizedForce = max(0, min(1, maxForce))
        let baseRadius = minRadius
        let maxRadiusMultiplier: CGFloat = 3.0 // 最大半径是最小半径的3倍
        return baseRadius + (baseRadius * maxRadiusMultiplier - baseRadius) * normalizedForce
    }

    /// 缓存点池容量
    private var cacheSize: Int = 4

    /// 跟踪的触摸
    private var trackedTouch: UITouch?

    /// 判断是否是 pencil 模式
    private var isPencil: Bool {
        trackedTouch?.type == .pencil
    }

    // MARK: - 初始化

    public init(params: Params = Params(), taperConfig: TaperConfig = TaperConfig()) {
        self.params = params
        self.taperConfig = taperConfig
    }

    // MARK: - 公共方法

    /// 清理状态
    public func clean() {
        confirmPath = CGMutablePath()
        cachePath = CGMutablePath()
        samplingPath = CGMutablePath()
        confirmedPoints.removeAll()
        cachingPoints.removeAll()
        renderContext = nil
        trackedTouch = nil
        taperState.reset()
    }

    /// 生成半径 - 修复递归调用问题
    public func generateRadiusWithForce(force: CGFloat, altitude: CGFloat? = nil) -> CGFloat {
        let adjustedForce = altitude != nil ? force * CGFloat(sin(Double(altitude!))) : force
        let normalizedForce = max(0, min(1, adjustedForce))

        // 直接计算半径范围，避免使用maxRadius属性
        let minRad = minRadius
        let maxRad = minRad * 3.0 // 最大半径是最小半径的3倍
        let radiusRange = maxRad - minRad

        return minRad + radiusRange * normalizedForce
    }

    /// 获取追加的触摸
    private func getAppendTouches(touches: Set<UITouch>?, event: UIEvent?) -> UITouch? {
        guard let touches = touches else { return nil }
        return touches.first
    }

    /// 更新点坐标信息，做一些预处理
    func updatePoint(point: ZDPointUnit, touches: Set<UITouch>?, event: UIEvent?, view: UIView?) -> ZDPointUnit {
        var newPoint: ZDPointUnit = point
        if let view = view,
           let touch = getAppendTouches(touches: touches, event: event) {
            let location = newPoint.location
            newPoint = ZDPointUnit(point: location, touch: touch)
            //使用速度转压力
            if let lastPoint = self.cachingPoints.last, self.isForceMode == false {
                let radius = speedToRadius(pointA: lastPoint.point, pointB: newPoint)
                newPoint = newPoint.withRadius(radius)
            } else if let radius = pointRadius(point) {
                //使用系统自带压力
                newPoint = newPoint.withRadius(radius)
                newPoint.estimatedProperties = touch.estimatedProperties
                newPoint.estimatedPropertiesExpectingUpdates = touch.estimatedPropertiesExpectingUpdates
                newPoint.altitude = touch.altitudeAngle
                newPoint.azimuth = touch.azimuthAngle(in: view)
            }
        }

        if newPoint.radius == nil,
           let radius = pointRadius(newPoint) {
            newPoint = newPoint.withRadius(radius)
        }

        return newPoint
    }

    /// 计算点半径
    public func pointRadius(_ point: ZDPointUnit) -> Optional<CGFloat> {
        generateRadiusWithForce(force: point.force, altitude: point.altitude)
    }

    /// 速度转半径
    func speedToRadius(pointA: ZDPointUnit, pointB : ZDPointUnit) -> CGFloat {
        guard let timeStamp1 = pointA.timeStamp,
              let timeStamp2 = pointB.timeStamp  else { return 0 }
        let interval = timeStamp2 - timeStamp1
        let speed = MathUtils.getSpeedBetweenTwoPointsWithTimeInterval(pointA: pointA.location, pointB: pointB.location, interval: interval)
        let preSpeedForce = MathUtils.newBrushTransformSpeedToForce(speed, params.calligraphyMaxForce, params.calligraphyMinForce, scale, fasterIsThick: true)
        let radius = generateRadiusWithForce(force: preSpeedForce)
        return radius
    }

    // MARK: - PathGeneratorType 协议实现

    public func began(point: ZDPointUnit, touches: Set<UITouch>? = nil, event: UIEvent? = nil) -> EffectArea {
        self.clean()
        guard trackedTouch == nil else {return .zero}
        trackedTouch = touches?.first

        let filterPointsFactor: CGFloat = 0.1 + params.calligraphyMotionSmooth * 0.5
        pathPolishTool.updateParameter(lim_a: 0.6/scale, lim_c: (params.radius * 2.0)*filterPointsFactor)
        return append(point: point, touches: touches, event: event, view: view)
    }

    public func push(point: ZDPointUnit, touches: Set<UITouch>? = nil, event: UIEvent? = nil) -> EffectArea {
        let effectArea = append(point: point, touches: touches, event: event, view: view)
        return effectArea
    }

    public func end(point: ZDPointUnit, touches: Set<UITouch>? = nil, event: UIEvent? = nil) -> EffectArea {
        let effectArea = append(point: point, touches: touches, event: event, view: view, isEnd: true)
        return effectArea
    }

    public func updateRelativeView(_ view: UIView) {
        self.view = view
    }

    public func pathRef() -> CGPath? {
        let path = CGMutablePath()
        path.addPath(self.confirmPath)
        path.addPath(self.cachePath)
        path.addPath(self.samplingPath)
        return path
    }

    public func avaliablePoints() -> [ZDPointUnit] {
        return (self.confirmedPoints + self.cachingPoints).map { $0.point }
    }

    // MARK: - 核心处理方法

    /// 添加点的核心方法
    func append(point: ZDPointUnit, touches: Set<UITouch>? = nil, event: UIEvent? = nil, view: UIView? = nil, isEnd: Bool = false) -> EffectArea {
        // 这里确保将点坐标的压力信息转换到圆的半径，后面都使用 半径信息，屏蔽压力
        let point: ZDPointUnit = updatePoint(point: point, touches: touches, event: event, view: view)
        assert(point.radius != nil)

        let ref = PointUnitRef(point: point)

        guard let newRef = shouldPeddingToCache(ref: ref, isEnd: isEnd) else {
            return handleTailedPointUpdate(point: point, touches: touches, event: event, view: view)
        }

        // 添加到缓存
        cachingPoints.append(newRef)

        // 如果缓存满了或者是结束，处理缓存的点
        if cachingPoints.count >= cacheSize || isEnd {
            return processCachedPoints(isEnd: isEnd)
        }

        return .zero
    }

    /// 判断是否应该添加到缓存
    private func shouldPeddingToCache(ref: PointUnitRef, isEnd: Bool) -> PointUnitRef? {
        // 如果是第一个点，直接添加
        if cachingPoints.isEmpty {
            return ref
        }

        // 检查距离过滤
        if let lastPoint = cachingPoints.last {
            let distance = ZDPointUnit.distance(p1: lastPoint.point, p2: ref.point)
            if distance < 1.0 && !isEnd {
                return nil // 距离太近，不添加
            }
        }

        return ref
    }

    /// 处理尾部点更新
    private func handleTailedPointUpdate(point: ZDPointUnit, touches: Set<UITouch>?, event: UIEvent?, view: UIView?) -> EffectArea {
        // 更新最后一个点的信息
        if let lastRef = cachingPoints.last {
            lastRef.update(point: point)
        }
        return .zero
    }

    /// 处理缓存的点
    private func processCachedPoints(isEnd: Bool) -> EffectArea {
        guard !cachingPoints.isEmpty else { return .zero }

        // 控制点半径变化
        let controlledPoints = controlPointUnitRadiusChanging(penddingPoints: cachingPoints, confirmPoints: confirmedPoints)

        // 生成路径
        let effectArea = generatePathFromPoints(points: controlledPoints, isEnd: isEnd)

        // 移动点从缓存到确认
        confirmedPoints.append(contentsOf: cachingPoints)
        cachingPoints.removeAll()

        return effectArea
    }

    /// 控制点单元半径变化
    fileprivate func controlPointUnitRadiusChanging(penddingPoints: [PointUnitRef], confirmPoints: [PointUnitRef]) -> [PointUnitRef] {
        if let lastPoint = confirmPoints.last?.point ?? penddingPoints.first?.point,
           var radiusA = pointRadius(lastPoint) {

            //计算改变量
            let deletaCount:CGFloat = params.calligraphyWidthSmooth * 4.0 + 1.0
            let maxRadius = self.maxRadius
            let minRadius = self.minRadius
            let delta: CGFloat = (maxRadius - minRadius) / deletaCount

            for p in penddingPoints {
                let radiusB = p.point.radius ?? 1.0
                var radius = radiusB
                if radiusA - radiusB >= delta {
                    //如果后面的点过小，则调大一点
                    radius = radiusA - delta
                } else if radiusB - radiusA >= delta {
                    //如果后面的点过大，则调小一点
                    radius = radiusA + delta
                } else  {
                    radius = radiusB
                }

                //最小宽度限制
                radius = max(minRadius, radius)

                p.update(point: p.point.withRadius(radius))

                //下个循环约束后面的点
                radiusA = radius
            }
        }

        return penddingPoints
    }

    /// 从点生成路径
    private func generatePathFromPoints(points: [PointUnitRef], isEnd: Bool) -> EffectArea {
        guard points.count >= 2 else { return .zero }

        // 简化实现：生成基本的圆形路径
        var effectArea: CGRect = .zero

        for pointRef in points {
            let point = pointRef.point
            let radius = point.radius ?? minRadius
            let circleRect = CGRect(x: point.location.x - radius,
                                  y: point.location.y - radius,
                                  width: radius * 2,
                                  height: radius * 2)

            let circlePath = ZDPathUtils.generateCircle(center: point.location, radius: radius)
            confirmPath.addPath(circlePath)

            effectArea = effectArea.union(circleRect)
        }

        return effectArea
    }
}
